* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif !important;
}

:root {
  --scrollbar-width: 8px;
  --scrollbar-track-color: transparent;
  --scrollbar-thumb-color: rgba(64, 158, 255, 0.3);
  --scrollbar-thumb-hover-color: rgba(102, 177, 255, 0.9);
  --scrollbar-border-radius: 4px;
}

html,
body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  overflow-y: overlay;
}

#app {
  width: 100%;
  min-height: 100vh;
  text-align: center;
  overflow-y: overlay;
}

::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
  background-color: transparent;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color);
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb-color);
  border-radius: var(--scrollbar-border-radius);
  background-clip: padding-box;
  border: 2px solid transparent;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover-color);
}

* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);
}

.el-scrollbar__wrap,
.el-scrollbar__view,
.scrollable-content,
.el-table__body-wrapper,
.el-select-dropdown__wrap,
.el-cascader-menu__wrap,
.el-dialog__body {
  overflow-y: overlay !important;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
}